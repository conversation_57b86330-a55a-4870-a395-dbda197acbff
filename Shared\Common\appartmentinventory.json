{"Succeeded": true, "Message": "Module configurations retrieved successfully", "Data": [{"Id": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "Name": "Apartment Inventory Management", "Icon": "home", "Description": "Apartment inventory management system", "IsActive": true, "DisplayOrder": 1, "Groups": [{"Id": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "Name": "Building", "Description": "Apartment building or complex", "DisplayOrder": 1, "IsCollapsible": true, "IsCollapsedByDefault": false, "IsActive": true, "Fields": [{"Id": "28c8c55c-38ce-4a7e-b595-7c879afbbfb4", "Name": "BuildingId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter building ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "51b0330d-3dc8-47c7-b1e2-004b0452ac1c", "Name": "BuildingName", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter building name", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "c41d9a1e-fa58-4938-9af9-723eb15163a2", "Name": "BuildingCode", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Enter building code", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "f44fefd7-c8ca-44d4-8356-4984870ef7ee", "Name": "BuildingDescription", "Description": null, "FieldType": 0, "IsRequired": false, "IsActive": true, "DisplayOrder": 4, "DefaultValue": "", "Placeholder": "Enter building description", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": false, "IsSortable": true, "IsVisibleInList": false, "Options": null, "DisplayOptions": null, "FieldGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [{"Id": "9f8b9d47-9c20-47b7-a75e-f8262bd0490a", "Name": "Floor", "Description": "Floor within the building", "DisplayOrder": 1, "IsCollapsible": true, "IsCollapsedByDefault": true, "IsActive": true, "Fields": [{"Id": "00b20c09-ad66-47d3-a6da-d4a9e741f52a", "Name": "FloorNumber", "Description": null, "FieldType": 1, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter floor number", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "9f8b9d47-9c20-47b7-a75e-f8262bd0490a", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "87c5baac-a396-4369-aa52-f5ff99ccbf17", "Name": "BuildingId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Enter building ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": false, "Options": null, "DisplayOptions": null, "FieldGroupId": "9f8b9d47-9c20-47b7-a75e-f8262bd0490a", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "9c2dd374-847a-42cc-8bfd-33627f07f1b3", "Name": "FloorId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter floor ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "9f8b9d47-9c20-47b7-a75e-f8262bd0490a", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [{"Id": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "Name": "Apartment", "Description": "Individual apartment unit", "DisplayOrder": 1, "IsCollapsible": true, "IsCollapsedByDefault": false, "IsActive": true, "Fields": [{"Id": "c98e9260-9d0a-4166-80e0-00b6f5915ea5", "Name": "ApartmentId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter apartment ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "27b4e39d-9969-409b-b493-3e364bae1eee", "Name": "FloorId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 4, "DefaultValue": "", "Placeholder": "Enter floor ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": false, "Options": null, "DisplayOptions": null, "FieldGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "3bdeebaf-9c52-4ff9-b6be-cf5b9ad4c4de", "Name": "UnitType", "Description": null, "FieldType": 5, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Select unit type", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": ["Studio", "1BR", "2BR", "3BR", "4BR", "Penthouse", "Loft", "Duplex"], "DisplayOptions": null, "FieldGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "b678b1a8-05d3-4c67-87f0-b76af1c2b978", "Name": "ApartmentNumber", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter apartment number", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [{"Id": "1420c977-787d-4131-8384-d64bde6c22d5", "Name": "Tenant", "Description": "Tenant information", "DisplayOrder": 1, "IsCollapsible": true, "IsCollapsedByDefault": false, "IsActive": true, "Fields": [{"Id": "4630c24a-67c1-49f9-b3c2-f108e47fed79", "Name": "TenantId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter tenant ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "1420c977-787d-4131-8384-d64bde6c22d5", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "9f0b5154-72cb-45c4-823c-a127a15f9c10", "Name": "LastName", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Enter last name", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "1420c977-787d-4131-8384-d64bde6c22d5", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "a93f536d-58d2-448c-ae96-19bd500a4536", "Name": "ApartmentId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 4, "DefaultValue": "", "Placeholder": "Enter apartment ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "1420c977-787d-4131-8384-d64bde6c22d5", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "fa2063b7-e9c0-4324-81a1-8d2491f28211", "Name": "FirstName", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter first name", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "1420c977-787d-4131-8384-d64bde6c22d5", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [], "ParentGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230725Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038743Z", "LastModifiedBy": null}, {"Id": "fc970a04-aa32-4054-8a05-5c049e6a6427", "Name": "Lease", "Description": "Lease agreement details", "DisplayOrder": 2, "IsCollapsible": true, "IsCollapsedByDefault": true, "IsActive": true, "Fields": [{"Id": "9dc2dbf4-f253-4018-a1c2-519aa8597d8f", "Name": "TenantId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter tenant ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "fc970a04-aa32-4054-8a05-5c049e6a6427", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "856a6590-3499-42b0-9b8f-0ed381b49447", "Name": "ApartmentId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Enter apartment ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "fc970a04-aa32-4054-8a05-5c049e6a6427", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "41f5be99-abce-43c0-a47f-61057233432f", "Name": "LeaseId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter lease ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "fc970a04-aa32-4054-8a05-5c049e6a6427", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [], "ParentGroupId": "ead95d98-e37e-4a8a-be8d-a96515e3e5ba", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230739Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038775Z", "LastModifiedBy": null}], "ParentGroupId": "9f8b9d47-9c20-47b7-a75e-f8262bd0490a", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230725Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038716Z", "LastModifiedBy": null}], "ParentGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230724Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038714Z", "LastModifiedBy": null}, {"Id": "b7c421f3-8a9c-4eae-b41c-c44367e67f56", "Name": "Amenity", "Description": "Building amenities", "DisplayOrder": 2, "IsCollapsible": true, "IsCollapsedByDefault": true, "IsActive": true, "Fields": [{"Id": "c53e8db1-a81d-4782-9473-a4c6ad372197", "Name": "AmenityName", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 2, "DefaultValue": "", "Placeholder": "Enter amenity name", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "b7c421f3-8a9c-4eae-b41c-c44367e67f56", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "5d3a7d67-0e23-405a-a2fe-272dcd8426b9", "Name": "BuildingId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 3, "DefaultValue": "", "Placeholder": "Enter building ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": false, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": false, "Options": null, "DisplayOptions": null, "FieldGroupId": "b7c421f3-8a9c-4eae-b41c-c44367e67f56", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}, {"Id": "7aa50984-885f-4b32-9f64-a4c732068404", "Name": "AmenityId", "Description": null, "FieldType": 0, "IsRequired": true, "IsActive": true, "DisplayOrder": 1, "DefaultValue": "", "Placeholder": "Enter amenity ID", "HelpText": null, "ValidationPattern": null, "ValidationMessage": null, "IsUnique": true, "IsSearchable": true, "IsFilterable": true, "IsSortable": true, "IsVisibleInList": true, "Options": null, "DisplayOptions": null, "FieldGroupId": "b7c421f3-8a9c-4eae-b41c-c44367e67f56", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "TenantId": null, "CreatedOn": "0001-01-01T00:00:00", "CreatedBy": null, "LastModifiedOn": null, "LastModifiedBy": null}], "ChildGroups": [], "ParentGroupId": "a5e02d9f-5955-4286-bc38-ed2825e72a16", "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230741Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038778Z", "LastModifiedBy": null}], "ParentGroupId": null, "ModuleConfigId": "d1f2ec06-0ad0-43d6-a016-9396d85c914b", "ModuleConfigName": "Apartment Inventory Management", "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230716Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.038393Z", "LastModifiedBy": null}], "Fields": [], "TenantId": null, "CreatedOn": "2025-05-18T19:08:30.230701Z", "CreatedBy": "40388993-a3a8-4054-a108-b36381e0eefa", "LastModifiedOn": "2025-05-18T19:08:30.037031Z", "LastModifiedBy": null}]}