namespace Shared.Database;

/// <summary>
/// Database schema names
/// </summary>
public static class SchemaNames
{
    /// <summary>
    /// Catalog schema
    /// </summary>
    public const string Catalog = "Catalog";

    /// <summary>
    /// Identity schema
    /// </summary>
    public const string Identity = "Identity";

    /// <summary>
    /// Auditing schema
    /// </summary>
    public const string Auditing = "Auditing";

    /// <summary>
    /// MultiTenancy schema
    /// </summary>
    public const string MultiTenancy = "MultiTenancy";

    /// <summary>
    /// ConfigurableModule schema
    /// </summary>
    public const string ConfigurableModule = "Inventory";
}
